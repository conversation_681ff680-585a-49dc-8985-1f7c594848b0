package com.cjx.ollama.utils.export;

public class EscapeUtil {
    private EscapeUtil() {
        throw new UnsupportedOperationException("EscapeUtil工具类不应该被实例化");
    }

    /**
     * 制表符分隔内容转义（处理制表符、换行）
     */
    public static String escapeTabSeparated(String value) {
        if (value == null) return "";
        return value.replace("\t", " ")
                .replace("\n", " ")
                .replace("\r", " ");
    }

    /**
     * CSV内容转义（处理逗号、引号、换行）
     */
    public static String escapeCsv(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
}