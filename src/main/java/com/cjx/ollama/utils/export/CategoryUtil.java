package com.cjx.ollama.utils.export;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import java.util.Map;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_POST_CATEGORY;

public class CategoryUtil {
    private CategoryUtil() {
        throw new UnsupportedOperationException("CategoryUtil工具类不应该被实例化");
    }

    /**
     * 文章分类与XML标签映射（扩展支持更多分类）
     */
    public static final Map<String, String> CATEGORY_XML_MAP = Map.ofEntries(
            Map.entry("百度", "<category domain=\"category\" nicename=\"baidu\"><![CDATA[百度]]></category>"),
            Map.entry("谷歌", "<category domain=\"category\" nicename=\"google\"><![CDATA[谷歌]]></category>"),
            Map.entry("雅虎", "<category domain=\"category\" nicename=\"yahoo\"><![CDATA[雅虎]]></category>"),
            Map.entry("搜狗", "<category domain=\"category\" nicename=\"sogou\"><![CDATA[搜狗]]></category>"),
            Map.entry("未分类", "<category domain=\"category\" nicename=\"uncategorized\"><![CDATA[未分类]]></category>")
    );

    /**
     * 处理分类默认值（空值时返回默认分类）
     */
    public static String getProcessedCategory(String category, String defaultCategory) {
        return StringUtils.isBlank(category) ? defaultCategory : category;
    }

    /**
     * 根据分类获取XML标签（无匹配时返回默认分类标签）
     */
    public static String getCategoryXmlTag(String category) {
        String processedCategory = getProcessedCategory(category, DEFAULT_POST_CATEGORY);
        return CATEGORY_XML_MAP.getOrDefault(processedCategory,
                CATEGORY_XML_MAP.get(DEFAULT_POST_CATEGORY));
    }
}